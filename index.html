<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="HAI Systems aide les repreneurs à rendre leurs acquisitions rentables rapidement via l'automatisation et l'IA. Diagnostic gratuit 30 min — plan d'action concret.">
    <meta property="og:title" content="HAI Systems — IA & automatisation pour repreneurs">
    <meta property="og:description" content="Diagnostic gratuit 30 min — plan d'action concret pour rendre vos acquisitions rentables rapidement.">
    <meta property="og:image" content="https://hai-systems.com/og-image.png">
    <meta property="og:url" content="https://hai-systems.com/">
    <meta name="twitter:card" content="summary_large_image">
    <meta name="keywords" content="repreneurs d'entreprise, automatisation PME, audit IA, optimisation post-reprise, intégration systèmes, acquisition">
    <title>HAI Systems — Automatisation & IA pour repreneurs | Diagnostic gratuit</title>
    
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Space+Grotesk:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500;600&display=swap" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        /* Reset et base */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Space Grotesk', sans-serif;
            line-height: 1.6;
            color: #e2e8f0;
            background: #0a0a0a;
            overflow-x: hidden;
        }
        
        /* Variables CSS - Palette Futuriste Rouge */
        :root {
            /* Couleurs principales */
            --bg-primary: #0a0a0a;
            --bg-secondary: #111111;
            --bg-tertiary: #1a1a1a;
            
            /* Couleurs de texte */
            --text-primary: #ffffff;
            --text-secondary: #e2e8f0;
            --text-muted: #94a3b8;
            
            /* Accents néon rouge */
            --neon-red: #ff0040;
            --neon-red-glow: rgba(255, 0, 64, 0.5);
            --neon-red-dark: #cc0033;
            --neon-red-light: #ff3366;
            
            /* Glassmorphism */
            --glass-bg: rgba(255, 255, 255, 0.05);
            --glass-border: rgba(255, 255, 255, 0.1);
            --glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            
            /* Ombres et effets */
            --shadow-glow: 0 0 20px var(--neon-red-glow);
            --shadow-soft: 0 4px 20px rgba(0, 0, 0, 0.5);
            --shadow-hard: 0 8px 40px rgba(0, 0, 0, 0.7);
            
            /* Transitions */
            --transition-fast: 0.2s ease;
            --transition-smooth: 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            --transition-bounce: 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
        }
        
        /* Typographie */
        .font-mono { font-family: 'JetBrains Mono', monospace; }
        
        .text-hero {
            font-size: clamp(3rem, 8vw, 6rem);
            font-weight: 700;
            line-height: 0.9;
            letter-spacing: -0.02em;
        }
        
        .text-xl {
            font-size: clamp(1.5rem, 4vw, 2.5rem);
            font-weight: 600;
            line-height: 1.2;
        }
        
        .text-lg {
            font-size: clamp(1.125rem, 2.5vw, 1.5rem);
            font-weight: 500;
            line-height: 1.4;
        }
        
        .text-base {
            font-size: 1.125rem;
            line-height: 1.6;
        }
        
        /* Utilitaires */
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 2rem;
        }
        
        .section {
            padding: 8rem 0;
        }
        
        .section-sm {
            padding: 4rem 0;
        }
        
        /* Navigation flottante */
        .navbar {
            position: fixed;
            top: 2rem;
            left: 50%;
            transform: translateX(-50%);
            z-index: 1000;
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: 50px;
            padding: 1.5rem 3rem;
            transition: var(--transition-smooth);
            width: 100%;
            max-width: 1400px;
        }
        
        .navbar:hover {
            background: rgba(255, 255, 255, 0.08);
            box-shadow: var(--shadow-soft);
        }
        
        .nav-container {
            display: grid;
            grid-template-columns: auto 1fr auto;
            align-items: center;
            gap: 4rem;
            width: 100%;
            max-width: 1200px;
        }

        .nav-links {
            display: flex;
            gap: 2.5rem;
            list-style: none;
            justify-content: center;
        }

        /* Menu hamburger pour mobile */
        .mobile-menu-toggle {
            display: none;
            background: none;
            border: none;
            color: var(--text-primary);
            font-size: 1.5rem;
            cursor: pointer;
            padding: 0.5rem;
            border-radius: 8px;
            transition: var(--transition-fast);
        }

        .mobile-menu-toggle:hover {
            background: rgba(255, 255, 255, 0.1);
        }

        .mobile-nav {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.95);
            backdrop-filter: blur(20px);
            z-index: 2000;
            padding: 2rem;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            gap: 2rem;
        }

        .mobile-nav.active {
            display: flex;
        }

        .mobile-nav-close {
            position: absolute;
            top: 2rem;
            right: 2rem;
            background: none;
            border: none;
            color: var(--text-primary);
            font-size: 2rem;
            cursor: pointer;
        }

        .mobile-nav-links {
            display: flex;
            flex-direction: column;
            gap: 2rem;
            text-align: center;
            list-style: none;
        }

        .mobile-nav-link {
            color: var(--text-primary);
            text-decoration: none;
            font-size: 1.5rem;
            font-weight: 500;
            transition: var(--transition-fast);
        }

        .mobile-nav-link:hover {
            color: var(--neon-red);
        }

        .logo {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--text-primary);
            text-decoration: none;
        }
        
        .nav-link {
            color: var(--text-muted);
            text-decoration: none;
            font-weight: 500;
            transition: var(--transition-fast);
            position: relative;
        }
        
        .nav-link:hover {
            color: var(--neon-red);
        }
        
        .nav-link::after {
            content: '';
            position: absolute;
            bottom: -4px;
            left: 0;
            width: 0;
            height: 2px;
            background: var(--neon-red);
            transition: var(--transition-smooth);
        }
        
        .nav-link:hover::after {
            width: 100%;
        }
        
        /* Boutons CTA */
        .btn {
            display: inline-flex;
            align-items: center;
            gap: 0.75rem;
            padding: 1rem 2rem;
            border: none;
            border-radius: 50px;
            font-weight: 600;
            text-decoration: none;
            text-align: center;
            cursor: pointer;
            transition: var(--transition-smooth);
            font-size: 1.1rem;
            position: relative;
            overflow: hidden;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, var(--neon-red), var(--neon-red-dark));
            color: var(--text-primary);
            box-shadow: var(--shadow-glow);
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 0 30px var(--neon-red-glow);
        }
        
        .btn-primary::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: var(--transition-smooth);
        }
        
        .btn-primary:hover::before {
            left: 100%;
        }
        
        .btn-secondary {
            background: var(--glass-bg);
            color: var(--text-primary);
            border: 1px solid var(--glass-border);
            backdrop-filter: blur(10px);
        }
        
        .btn-secondary:hover {
            background: rgba(255, 255, 255, 0.1);
            border-color: var(--neon-red);
            box-shadow: 0 0 20px rgba(255, 0, 64, 0.3);
        }
        
        .btn-lg {
            padding: 1.5rem 3rem;
            font-size: 1.25rem;
        }

        .btn-md {
            padding: 1rem 2rem;
            font-size: 1rem;
        }
        
        /* Hero Section */
        .hero {
            min-height: 100vh;
            display: flex;
            align-items: center;
            position: relative;
            z-index: 50;
            background: radial-gradient(ellipse at center, rgba(255, 0, 64, 0.1) 0%, transparent 70%);
            padding-top: 10rem;
        }
        
        .hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 20% 80%, rgba(255, 0, 64, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 0, 64, 0.05) 0%, transparent 50%);
            pointer-events: none;
            transform: translateY(var(--parallax-y, 0));
            transition: transform 0.2s linear;
        }
        
        .hero-content {
            position: relative;
            z-index: 20;
            text-align: center;
            max-width: 1000px;
            margin: 0 auto;
            visibility: hidden;
            opacity: 0;
            transition: opacity 220ms ease;
        }
        
        .hero-content.js-visible {
            visibility: visible;
            opacity: 1;
        }
        
        .hero-title {
            margin-bottom: 2rem;
            animation: fadeInUp 1s ease-out;
        }
        
        .hero-subtitle {
            color: var(--text-muted);
            margin-bottom: 3rem;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
            animation: fadeInUp 1s ease-out 0.2s both;
        }
        
        .hero-cta {
            display: flex;
            gap: 1.5rem;
            justify-content: center;
            flex-wrap: wrap;
            animation: fadeInUp 1s ease-out 0.4s both;
        }
        
        .neon-text {
            background: linear-gradient(135deg, var(--neon-red), var(--neon-red-light));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            filter: drop-shadow(0 0 10px var(--neon-red-glow));
        }
        
        /* Cartes glassmorphism */
        .glass-card {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: 20px;
            padding: 3rem;
            transition: var(--transition-smooth);
            position: relative;
            overflow: hidden;
        }
        
        .glass-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, var(--neon-red), transparent);
            opacity: 0;
            transition: var(--transition-smooth);
        }
        
        .glass-card:hover {
            transform: translateY(-10px);
            background: rgba(255, 255, 255, 0.08);
            box-shadow: var(--shadow-hard);
        }
        
        .glass-card:hover::before {
            opacity: 1;
        }

        /* Icônes pour les cartes */
        .card-icon {
            width: 3rem;
            height: 3rem;
            background: linear-gradient(135deg, var(--neon-red), var(--neon-red-dark));
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 1.5rem;
            font-size: 1.25rem;
            color: var(--text-primary);
            box-shadow: var(--shadow-glow);
        }

        /* Numéros d'étapes pour le process */
        .step-number {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 2.5rem;
            height: 2.5rem;
            background: linear-gradient(135deg, var(--neon-red), var(--neon-red-dark));
            border-radius: 50%;
            color: var(--text-primary);
            font-weight: 700;
            font-size: 1.1rem;
            margin-right: 1rem;
            box-shadow: var(--shadow-glow);
            flex-shrink: 0;
        }

        .step-title {
            display: flex;
            align-items: center;
            margin-bottom: 1rem;
        }
        
        /* Grille de services */
        .services-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 2rem;
            margin-top: 4rem;
        }
        
        .service-icon {
            width: 4rem;
            height: 4rem;
            background: linear-gradient(135deg, var(--neon-red), var(--neon-red-dark));
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 2rem;
            font-size: 1.5rem;
            color: var(--text-primary);
            box-shadow: var(--shadow-glow);
        }
        
        /* Témoignages */
        .testimonials-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 2rem;
            margin-top: 4rem;
        }
        
        .testimonial {
            position: relative;
        }
        
        .testimonial-quote {
            font-size: 1.25rem;
            font-style: italic;
            margin-bottom: 2rem;
            color: var(--text-secondary);
        }
        
        .testimonial-author {
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        
        .author-avatar {
            width: 3rem;
            height: 3rem;
            background: linear-gradient(135deg, var(--neon-red), var(--neon-red-dark));
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 700;
            color: var(--text-primary);
        }
        
        .results-badge {
            background: linear-gradient(135deg, var(--neon-red), var(--neon-red-dark));
            color: var(--text-primary);
            padding: 0.75rem 1.5rem;
            border-radius: 50px;
            font-weight: 600;
            margin-top: 1.5rem;
            display: inline-block;
            box-shadow: var(--shadow-glow);
        }

        .result-highlight {
            color: var(--neon-red);
            font-weight: 700;
            font-size: 1.1em;
            text-shadow: 0 0 10px var(--neon-red-glow);
        }
        
        /* CTA Final */
        .cta-section {
            background: linear-gradient(135deg, rgba(255, 0, 64, 0.1), rgba(255, 0, 64, 0.05));
            border: 1px solid rgba(255, 0, 64, 0.2);
            border-radius: 30px;
            padding: 4rem;
            text-align: center;
            position: relative;
            overflow: hidden;
        }
        
        .cta-section::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: conic-gradient(from 0deg, transparent, rgba(255, 0, 64, 0.1), transparent);
            animation: rotate 20s linear infinite;
            pointer-events: none;
        }
        
        .cta-content {
            position: relative;
            z-index: 1;
        }
        
        /* Footer avec section À propos intégrée */
        .footer {
            background: var(--bg-secondary);
            border-top: 1px solid var(--glass-border);
            padding: 4rem 0;
        }

        .footer-content {
            display: grid;
            grid-template-columns: 1fr auto 1fr;
            align-items: start;
            gap: 3rem;
            max-width: 1200px;
            margin: 0 auto;
        }

        .footer-about {
            text-align: center;
        }

        .footer-about h2 {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: var(--text-primary);
        }

        .footer-about h2 .neon-text {
            font-size: 1.5rem;
        }

        .footer-about p {
            color: var(--text-muted);
            line-height: 1.6;
            margin-bottom: 0.75rem;
        }

        .footer-divider {
            width: 1px;
            height: 100%;
            min-height: 200px;
            background: linear-gradient(to bottom, var(--neon-red), transparent);
            opacity: 0.3;
            position: relative;
        }

        .footer-divider::before {
            content: '';
            position: absolute;
            top: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 3px;
            height: 20px;
            background: var(--neon-red);
            border-radius: 2px;
            box-shadow: 0 0 10px var(--neon-red-glow);
        }

        .footer-info {
            text-align: center;
        }

        .footer-branding {
            margin-bottom: 1.5rem;
        }

        .footer-branding p {
            font-weight: bold;
            color: var(--neon-red);
            font-size: 1.1rem;
            margin-bottom: 1rem;
        }

        .footer-contact {
            margin-bottom: 1.5rem;
        }

        .footer-contact p {
            margin-bottom: 0.5rem;
        }

        .footer-nav {
            margin: 1.5rem 0;
        }

        .footer-nav a {
            color: var(--text-muted);
            text-decoration: none;
            margin: 0 0.5rem;
            transition: var(--transition-fast);
        }

        .footer-nav a:hover {
            color: var(--neon-red);
        }

        .footer-copyright {
            font-size: 0.85rem;
            color: var(--text-muted);
        }

        .footer-copyright a {
            color: var(--text-muted);
            text-decoration: none;
            transition: var(--transition-fast);
        }

        .footer-copyright a:hover {
            color: var(--neon-red);
        }
        
        .footer-links {
            display: flex;
            gap: 2rem;
            list-style: none;
        }
        
        .footer-link {
            color: var(--text-muted);
            text-decoration: none;
            transition: var(--transition-fast);
        }
        
        .footer-link:hover {
            color: var(--neon-red);
        }

        /* Liens de contact (utilisable dans plusieurs sections) */
        .contact-link {
            color: var(--text-muted);
            text-decoration: none;
            transition: var(--transition-fast);
        }

        .contact-link:hover {
            color: var(--neon-red);
        }

        /* FAQ Accordéons */
        .faq-container {
            max-width: 900px;
            margin: 0 auto;
        }

        .faq-item {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: 12px;
            margin-bottom: 1rem;
            overflow: hidden;
            transition: var(--transition-smooth);
        }

        .faq-item:hover {
            background: rgba(255, 255, 255, 0.08);
        }

        .faq-question {
            width: 100%;
            background: none;
            border: none;
            padding: 1.5rem;
            text-align: left;
            color: var(--text-primary);
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: var(--transition-fast);
        }

        .faq-question:hover {
            color: var(--neon-red);
        }

        .faq-icon {
            font-size: 1rem;
            transition: var(--transition-smooth);
        }

        .faq-item.active .faq-icon {
            transform: rotate(180deg);
        }

        .faq-answer {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease;
        }

        .faq-answer.active {
            max-height: 200px;
        }

        .faq-answer-content {
            padding: 0 1.5rem 1.5rem;
            color: var(--text-muted);
            line-height: 1.6;
        }

        /* Formulaire amélioré */
        .form-field {
            position: relative;
            margin-bottom: 1rem;
        }

        .form-label {
            position: absolute;
            left: -9999px;
            width: 1px;
            height: 1px;
            overflow: hidden;
        }

        .required-indicator {
            color: var(--neon-red);
            margin-left: 0.25rem;
        }

        .form-error {
            background: rgba(255, 0, 64, 0.1);
            border: 1px solid rgba(255, 0, 64, 0.3);
            color: var(--neon-red);
            padding: 1rem;
            border-radius: 8px;
            margin-top: 1rem;
            font-size: 0.95rem;
            text-align: center;
        }
        
        /* Animations */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        @keyframes rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        
        .animate-pulse {
            animation: pulse 2s infinite;
        }
        
        /* Micro-interactions */
        .interactive-element {
            transition: var(--transition-smooth);
        }
        
        .interactive-element:hover {
            transform: scale(1.02);
        }
        
        /* Responsive */
        @media (max-width: 768px) {
            .container {
                padding: 0 1rem;
            }

            .section {
                padding: 4rem 0;
            }

            .navbar {
                top: 1rem;
                padding: 0.75rem 1.5rem;
                min-width: auto;
                width: calc(100% - 2rem);
            }

            .nav-container {
                gap: 1rem;
            }

            .nav-links {
                display: none;
            }

            .mobile-menu-toggle {
                display: block;
            }
            .hero {
                padding-top: 8rem;
            }

            .hero-cta {
                flex-direction: column;
                align-items: center;
            }

            .services-grid,
            .testimonials-grid {
                grid-template-columns: 1fr;
            }

            .glass-card {
                padding: 2rem;
            }

            .cta-section {
                padding: 2rem;
            }

            /* Footer responsive */
            .footer-content {
                grid-template-columns: 1fr;
                gap: 2rem;
                text-align: center;
            }

            .footer-about {
                text-align: center;
                order: 1;
            }

            .footer-about h2 {
                margin-bottom: 1.5rem;
            }

            .footer-about p {
                max-width: none;
                margin-bottom: 1rem;
            }

            .footer-divider {
                display: none;
            }

            .footer-info {
                text-align: center;
                order: 2;
            }

            .footer-branding {
                margin-bottom: 2rem;
            }

            .footer-contact {
                margin-bottom: 2rem;
            }

            .footer-nav {
                margin: 2rem 0;
            }

            .footer-copyright {
                margin-top: 2rem;
            }

            .faq-answer.active {
                max-height: 300px;
            }
        }
        
        @media (max-width: 480px) {
            .text-hero {
                font-size: 2.5rem;
            }

            .btn {
                padding: 0.875rem 1.5rem;
            }

            .btn-lg {
                padding: 1.25rem 2rem;
            }

            /* Responsive improvements for merged section */
            .performance-indicator {
                font-size: 0.8rem;
                padding: 0.4rem 0.8rem;
                margin-top: 0.75rem;
            }

            .results-badge {
                font-size: 0.85rem;
                padding: 0.6rem 1.2rem;
                margin-top: 1rem;
                text-align: center;
                display: block;
                width: fit-content;
                margin-left: auto;
                margin-right: auto;
            }

            .service-icon {
                width: 3rem;
                height: 3rem;
                font-size: 1.2rem;
                margin-bottom: 1.5rem;
            }

            .card-icon {
                width: 2.5rem;
                height: 2.5rem;
                font-size: 1rem;
                margin-bottom: 1rem;
            }

            .step-number {
                width: 2rem;
                height: 2rem;
                font-size: 0.9rem;
                margin-right: 0.75rem;
            }
        }
        
        /* Effets de survol avancés */
        .hover-glow:hover {
            box-shadow: 0 0 30px var(--neon-red-glow);
        }
        
        .hover-lift:hover {
            transform: translateY(-5px);
        }
        
        /* Indicateurs de performance */
        .performance-indicator {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            background: rgba(34, 197, 94, 0.1);
            color: #22c55e;
            padding: 0.5rem 1rem;
            border-radius: 50px;
            font-weight: 600;
            font-size: 0.875rem;
            border: 1px solid rgba(34, 197, 94, 0.2);
        }
        
        /* Éléments décoratifs */
        .decoration-dot {
            width: 4px;
            height: 4px;
            background: var(--neon-red);
            border-radius: 50%;
            display: inline-block;
            margin: 0 0.5rem;
            animation: pulse 2s infinite;
        }

        /* Formulaire de diagnostic */
        #diagnostic-form {
            display: flex;
            flex-direction: column;
            gap: 1rem;
            max-width: 400px;
            margin: 0 auto;
            margin-bottom: 1.5rem;
        }

        #diagnostic-form input,
        #diagnostic-form select,
        #diagnostic-form textarea {
            padding: 1rem;
            border: 1px solid var(--glass-border);
            border-radius: 12px;
            background: var(--glass-bg);
            backdrop-filter: blur(10px);
            color: var(--text-primary);
            font-family: 'Space Grotesk', sans-serif;
            font-size: 1rem;
            transition: var(--transition-fast);
            resize: vertical;
        }

        #diagnostic-form input:focus,
        #diagnostic-form select:focus,
        #diagnostic-form textarea:focus {
            outline: none;
            border-color: var(--neon-red);
            box-shadow: 0 0 0 2px rgba(255, 0, 64, 0.2);
        }

        #diagnostic-form input::placeholder,
        #diagnostic-form textarea::placeholder {
            color: var(--text-muted);
        }

        #diagnostic-form button {
            margin-top: 0.5rem;
            padding: 1rem 2rem;
            align-self: center;
        }
    </style>
</head>
<body>
    <!-- Navigation flottante -->
    <nav class="navbar">
        <div class="nav-container">
            <a href="#" class="logo">HAI Systems</a>
            <ul class="nav-links">
                <li><a href="#accueil" class="nav-link">Accueil</a></li>
                <li><a href="#services" class="nav-link">Services</a></li>
                <li><a href="#process" class="nav-link">Process</a></li>
                <li><a href="#etudes-cas" class="nav-link">Études de cas</a></li>
                <li><a href="#pourquoi" class="nav-link">Pourquoi</a></li>
            </ul>
            <button class="mobile-menu-toggle" aria-label="Menu mobile">
                <i class="fas fa-bars"></i>
            </button>
            <a href="#contact" class="btn btn-primary" aria-label="Réserver un diagnostic gratuit (30 min)">
                <i class="fas fa-rocket"></i>
                Diagnostic Gratuit
            </a>
        </div>
    </nav>

    <!-- Menu mobile -->
    <div class="mobile-nav" id="mobile-nav">
        <button class="mobile-nav-close" aria-label="Fermer le menu">
            <i class="fas fa-times"></i>
        </button>
        <ul class="mobile-nav-links">
            <li><a href="#accueil" class="mobile-nav-link">Accueil</a></li>
            <li><a href="#problèmes" class="mobile-nav-link">Problèmes</a></li>
            <li><a href="#services" class="mobile-nav-link">Services</a></li>
            <li><a href="#process" class="mobile-nav-link">Process</a></li>
            <li><a href="#etudes-cas" class="mobile-nav-link">Études de cas</a></li>
            <li><a href="#pourquoi" class="mobile-nav-link">Pourquoi</a></li>
        </ul>
        <a href="#contact" class="btn btn-primary btn-lg">
            <i class="fas fa-rocket"></i>
            Diagnostic Gratuit
        </a>
    </div>
    <!-- Hero Section -->
    <section class="hero" id="accueil">
      <div class="container" role="main">
        <div class="hero-content">
          <div id="hero-title-container">
            <h1 class="text-hero hero-title">
              On repère. On automatise. <br><span class="neon-text">On fait gagner de l'argent.</span>
            </h1>
          </div>
          <div id="hero-subtitle-container">
            <p class="text-lg hero-subtitle">
              Solutions (automatisation et IA) clés en main pour repreneurs et PME : diagnostic gratuit de 30 minutes et plan d'action concret.
            </p>
          </div>
          <div class="hero-cta">
            <a id="hero-cta-button" href="#contact" class="btn btn-primary btn-lg hover-glow" aria-label="Réserver un diagnostic gratuit (30 min)">
              <i class="fas fa-chart-line"></i>
              Réserver un diagnostic gratuit
            </a>
            <a href="#services" class="btn btn-secondary btn-md">
              <i class="fas fa-play"></i>
              Voir nos solutions
            </a>
          </div>
        </div>
      </div>
    </section>

    <!-- Services -->
    <section class="section" id="services">
      <div class="container">
        <div style="text-align:center; margin-bottom:2rem;">
          <h2 class="text-xl">Solutions <span class="neon-text">Haute Performance</span></h2>
          <p class="text-base" style="color:var(--text-muted); max-width:700px; margin: 0 auto;">
            Pas de techno pour la techno : des actions concrètes qui réduisent les coûts et augmentent la marge.
          </p>
        </div>

        <div class="services-grid">
          <div class="glass-card interactive-element">
            <div class="service-icon"><i class="fas fa-search"></i></div>
            <h3 class="text-lg">Diagnostic & plan d'action</h3>
            <p style="color:var(--text-muted)">Audit priorisé par ROI — livrable opérationnel et prêt à exécuter.</p>
          </div>

          <div class="glass-card interactive-element">
            <div class="service-icon"><i class="fas fa-cogs"></i></div>
            <h3 class="text-lg">Implémentation clé en main</h3>
            <p style="color:var(--text-muted)">On développe, on intègre, on teste — vous recevez une solution prête à l'emploi.</p>
          </div>

          <div class="glass-card interactive-element">
            <div class="service-icon"><i class="fas fa-sync-alt"></i></div>
            <h3 class="text-lg">Maintenance & optimisation</h3>
            <p style="color:var(--text-muted)">Monitoring, itérations et améliorations pour maximiser le ROI.</p>
          </div>
        </div>
      </div>
    </section>

    <!-- Process -->
    <section class="section" id="process">
      <div class="container">
        <div style="text-align:center; margin-bottom:2rem;">
          <h2 class="text-xl">Comment ça se passe — <span class="neon-text">4 étapes claires</span></h2>
          <p class="text-base" style="color:var(--text-muted); max-width:800px; margin:0 auto;">
            Un parcours simple, orienté ROI : du diagnostic à la mise en production, puis au suivi.
          </p>
        </div>

        <div class="services-grid">
          <div class="glass-card">
            <div class="card-icon">
              <i class="fas fa-tasks"></i>
            </div>
            <h3 class="text-lg">1. Appel découverte — 30 min</h3>
            <p style="color:var(--text-muted)">Comprendre votre reprise, vos chiffres clés et vos priorités.</p>
          </div>
          <div class="glass-card">
            <h3 class="text-lg">2. Diagnostic & plan d'action</h3>
            <p style="color:var(--text-muted)">Livrable priorisé par ROI : feuille de route concrète.</p>
          </div>
          <div class="glass-card">
            <h3 class="text-lg">3. Implémentation clé en main</h3>
            <p style="color:var(--text-muted)">On développe, intègre et livre : vous n'avez rien à gérer techniquement.</p>
          </div>
          <div class="glass-card">
            <h3 class="text-lg">4. Suivi & optimisation</h3>
            <p style="color:var(--text-muted)">Monitoring, KPIs et itérations pour maximiser le retour.</p>
          </div>
        </div>
      </div>
    </section>

    <!-- Études de cas -->
    <section class="section" id="etudes-cas">
      <div class="container">
        <div style="text-align:center; margin-bottom:2rem;">
          <h2 class="text-xl">Études de cas — <span class="neon-text">résultats concrets</span></h2>
          <p class="text-base" style="color:var(--text-muted); max-width:800px; margin:0 auto;">
            Des transformations mesurables : économies, productivité, trésorerie. Études en cours — premiers résultats disponibles.
          </p>
        </div>

        <div class="testimonials-grid">
          <!-- Template case -->
          <div class="glass-card">
            <h3 class="text-lg">[Client] — [Secteur]</h3>
            <p style="color:var(--text-muted); margin-bottom:1rem;">Contexte : brève description (reprise, problématique)</p>
            <p style="color:var(--text-muted);"><strong>Solution :</strong> automatisation du flux X + intégration Y.</p>
            <p style="color:var(--neon-red); font-weight:600; margin-top:1rem;">Résultat : -25% coûts • ROI estimé 4 mois</p>
          </div>

            <div class="card-icon">
              <i class="fas fa-unlink"></i>
            </div>
          <!-- If you want 1 fictional example to add credibility -->
          <div class="glass-card">
            <h3 class="text-lg">PME Distribution — Reprise 2024</h3>
            <p style="color:var(--text-muted);">Problème : traitement manuel des commandes, erreurs fréquentes.</p>
            <div class="card-icon">
              <i class="fas fa-brain"></i>
            </div>
            <p style="color:var(--text-muted);"><strong>Solution :</strong> automatisation du flux commandes + intégration ERP.</p>
            <p style="color:var(--neon-red); font-weight:600; margin-top:1rem;">Résultat : -40% d'erreurs • -25% temps de traitement</p>
          </div>
        </div>
      </div>
    </section>

    <!-- Pourquoi HAI Systems - Merged Section -->
    <section class="section-sm" id="pourquoi">
      <div class="container">
        <div style="text-align:center; margin-bottom:2rem;">
          <h2 class="text-xl">Pourquoi HAI Systems — <span class="neon-text">L'excellence opérationnelle</span></h2>
          <p class="text-base" style="color:var(--text-muted); max-width:700px; margin: 0 auto;">
            Nous transformons vos acquisitions en succès rentables. Notre approche "Hitman" — rapidité, précision, exécution — génère du cash dès les premiers mois.
          </p>
        </div>

        <div class="services-grid" style="margin-top:3rem;">
          <div class="glass-card interactive-element">
            <div class="service-icon"><i class="fas fa-chart-line"></i></div>
            <h3 class="text-lg">ROI Avant Tout</h3>
            <p style="color:var(--text-muted)">Chaque action évaluée par son impact financier. Priorisation systématique des optimisations à haut retour.</p>
            <div class="performance-indicator" style="margin-top:1rem;">
              <i class="fas fa-bolt"></i>
              ROI moyen : 3-6 mois
            </div>
          </div>

          <div class="glass-card interactive-element">
            <div class="service-icon"><i class="fas fa-rocket"></i></div>
            <h3 class="text-lg">Exécution Éclair</h3>
            <p style="color:var(--text-muted)">Du diagnostic au déploiement : rapidité et précision. Surnommé "Hitman" pour notre qualité d'exécution.</p>
            <div class="performance-indicator" style="margin-top:1rem;">
              <i class="fas fa-clock"></i>
              Mise en production : 2-4 semaines
            </div>
          </div>

          <div class="glass-card interactive-element">
            <div class="service-icon"><i class="fas fa-tools"></i></div>
            <h3 class="text-lg">Solutions Clé en Main</h3>
            <p style="color:var(--text-muted)">Design → développement → intégration → maintenance. Vous recevez une solution prête à l'emploi.</p>
            <div class="performance-indicator" style="margin-top:1rem;">
              <i class="fas fa-shield-alt"></i>
              Support technique inclus
            </div>
          </div>

          <div class="glass-card interactive-element">
            <div class="service-icon"><i class="fas fa-trophy"></i></div>
            <h3 class="text-lg">Résultats Garantis</h3>
            <p style="color:var(--text-muted)">Transformations mesurables : réduction des coûts, augmentation de la productivité, optimisation de trésorerie.</p>
            <div class="results-badge" style="margin-top:1rem;">
              <i class="fas fa-chart-bar"></i>
              -25% coûts • +40% productivité
            </div>
          </div>
        </div>

        <div style="margin-top:2rem; text-align:center;">
          <a href="#contact" class="btn btn-primary btn-lg hover-glow" style="padding: 1.5rem 4rem;">
            <i class="fas fa-rocket"></i>
            Démarrer la transformation
          </a>
        </div>
      </div>
    </section>

    <!-- FAQ -->
    <section class="section-sm" id="faq">
      <div class="container">
        <h2 class="text-xl" style="text-align:center; margin-bottom:1rem;">FAQ — questions fréquentes</h2>
        <div style="max-width:900px; margin:0 auto; color:var(--text-muted);">
          <p><strong>Le diagnostic est-il vraiment gratuit ?</strong><br>Oui — 30 min d'échange + plan d'action synthétique offert, sans engagement.</p>
          <p><strong>Faut-il des compétences techniques en interne ?</strong><br>Non. Nous livrons des solutions prêtes à l'emploi et prenons en charge l'intégration et la maintenance.</p>
          <p><strong>Combien de temps pour voir un ROI ?</strong><br>Selon le projet : gains mesurables souvent en 1–3 mois pour optimisations prioritaires.</p>
          <p><strong>Quelle taille d'entreprise ciblez-vous ?</strong><br>PME & repreneurs — nous sommes spécialisés en post-reprise.</p>
        </div>
      </div>
    </section>

    <!-- CTA Final -->
    <section class="section-sm" id="contact">
      <div class="container">
        <div class="cta-section">
          <div class="cta-content">
            <h2 class="text-xl">Prêt pour la <span class="neon-text">transformation</span> ?</h2>
            <p class="text-base" style="color:var(--text-muted); margin-bottom:1.5rem; max-width:600px; margin-left:auto; margin-right:auto;">
              Diagnostic gratuit de 30 minutes. Plan d'action clair et priorisé par ROI — sans engagement.
            </p>

            <!-- Coordonnées directes -->
            <p style="margin-bottom:1rem;">
              📧 <a href="mailto:<EMAIL>" class="contact-link"><EMAIL></a><br>
              📞 <a href="tel:+33767199919" class="contact-link">+33 7 67 19 99 19</a>
            </p>

            <!-- Formulaire Airtable -->
            <form id="diagnostic-form">
              <input type="text" name="name" placeholder="Nom complet" required>
              <input type="text" name="company" placeholder="Entreprise" required>
              <input type="email" name="email" placeholder="Email pro" required>
              <input type="text" name="phone" placeholder="Téléphone (optionnel)">
              <select name="status">
                <option value="reprise">Reprise récente</option>
                <option value="optimisation">Optimisation</option>
                <option value="autre">Autre</option>
              </select>
              <textarea name="context" placeholder="Résumé de votre situation (optionnel - pour qu'on sache comment vous aider au mieux)" rows="4"></textarea>
              <button type="submit" class="btn btn-primary" aria-label="Réserver un diagnostic gratuit (30 min)">Réserver mon diagnostic</button>
            </form>

            <div style="margin-top:1.25rem; color:var(--text-muted); font-size:0.95rem;">
              <i class="fas fa-check" style="color:var(--neon-red);"></i> Gratuit & sans engagement
              <span class="decoration-dot"></span>
              <i class="fas fa-clock" style="color:var(--neon-red);"></i> Réponse sous 24h
            </div>
          </div>
        </div>
      </div>
    </section>



    <!-- Footer -->
    <section id="footer" class="section-sm">
      <div class="container">
        <footer class="footer">
          <div class="footer-content">
            <!-- Section À propos (gauche) -->
            <div class="footer-about">
              <h2><span class="neon-text">À propos</span></h2>
              <p>HAI Systems conçoit et déploie des systèmes d'automatisation et d'IA pour les repreneurs qui veulent transformer une acquisition en business rentable — vite.</p>
              <p>Approche pragmatique : diagnostic, exécution, optimisation. Surnommé "Hitman" par certains clients pour la qualité d'exécution — discipline, précision, efficacité.</p>
            <div class="step-title">
              <span class="step-number">1</span>
              <h3 class="text-lg">Appel découverte — 30 min</h3>
            </div>

            <!-- Diviseur central -->
            <div class="footer-divider"></div>
            <div class="step-title">
              <span class="step-number">2</span>
              <h3 class="text-lg">Diagnostic & plan d'action</h3>
            </div>
            <!-- Informations existantes (droite) -->
            <div class="footer-info">
              <div class="footer-branding">
                <h2><span class="neon-text">HAI Systems</span></h2>
              </div>
              <div class="footer-contact">
                <p>
                  📧 <a href="mailto:<EMAIL>" class="footer-link"><EMAIL></a><br>
                  📞 <a href="tel:+33767199919" class="footer-link">+33 7 67 19 99 19</a>
                </p>
              </div>
              <nav class="footer-nav">
                <a href="#accueil" class="footer-link">Accueil</a> •
                <a href="#services" class="footer-link">Services</a> •
                <a href="#process" class="footer-link">Process</a> •
                <a href="#etudes-cas" class="footer-link">Études de cas</a> •
                <a href="#pourquoi" class="footer-link">Pourquoi</a> •
                <a href="#contact" class="footer-link">Contact</a>
              </nav>
              <div class="footer-copyright">
                <p>© 2025 HAI Systems. Tous droits réservés. | <a href="#" class="footer-link">Mentions légales</a></p>
            <div class="step-title">
              <span class="step-number">3</span>
              <h3 class="text-lg">Implémentation clé en main</h3>
            <div class="step-title">
              <span class="step-number">4</span>
              <h3 class="text-lg">Suivi & optimisation</h3>
            </div>
            </div>
          </div>
        </footer>
      </div>
    </section>

    <script>
        // Animations au scroll
        function animateOnScroll() {
            const elements = document.querySelectorAll('.glass-card, .testimonial');
            
            elements.forEach((element, index) => {
                const elementTop = element.getBoundingClientRect().top;
                const windowHeight = window.innerHeight;
                
                if (elementTop < windowHeight * 0.8) {
                    setTimeout(() => {
                        element.style.opacity = '1';
            <p class="result-highlight" style="margin-top:1rem;">Résultat : -40% d'erreurs • -25% temps de traitement</p>
                    }, index * 100);
                }

        <div style="text-align:center; margin-top:3rem;">
          <a href="#contact" class="btn btn-primary btn-lg hover-glow">
            <i class="fas fa-rocket"></i>
            Obtenir des résultats similaires
          </a>
        </div>
            });
            <p class="result-highlight" style="margin-top:1rem;">Résultat : -25% coûts • ROI estimé 4 mois</p>

        // Smooth scroll
        function initSmoothScroll() {
            const links = document.querySelectorAll('a[href^="#"]');
            
            links.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    
                    const targetId = this.getAttribute('href');
                    const targetSection = document.querySelector(targetId);
                    
                    if (targetSection) {
                        const offsetTop = targetSection.offsetTop - 100;
                        
                        window.scrollTo({
                            top: offsetTop,
                            behavior: 'smooth'
                        });
                    }
                });
            });
        }

        // utilitaire pour récupérer UTM si présent
        function getUTMParams() {
            const urlParams = new URLSearchParams(window.location.search);
            return {
                utm_source: urlParams.get('utm_source') || '',
                utm_campaign: urlParams.get('utm_campaign') || ''
            };
        }

        const form = document.getElementById('diagnostic-form');
        if (form) {
            const submitBtn = form.querySelector('button[type="submit"]');
            const errorElId = 'form-error-msg';
            let errEl = document.getElementById(errorElId);
            if (!errEl) {
                errEl = document.createElement('div');
                errEl.id = errorElId;
                errEl.style.color = '#ff7b8a';
                errEl.style.marginTop = '0.75rem';
                errEl.style.fontSize = '0.95rem';
                form.appendChild(errEl);
            }

            form.addEventListener('submit', async (e) => {
                e.preventDefault();
                errEl.textContent = '';
                if (submitBtn) submitBtn.disabled = true;

                const data = Object.fromEntries(new FormData(form).entries());
                const utm = getUTMParams();

                try {
                    const res = await fetch('/api/lead', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ ...data, ...utm })
                    });

                    const json = await res.json();

                    if (res.ok) {
                        if (typeof gtag === 'function') {
        <div class="faq-container">
          <div class="faq-item">
            <button class="faq-question">
              Le diagnostic est-il vraiment gratuit ?
              <i class="fas fa-chevron-down faq-icon"></i>
            </button>
            <div class="faq-answer">
              <div class="faq-answer-content">
                Oui — 30 min d'échange + plan d'action synthétique offert, sans engagement.
              </div>
            </div>
          </div>

          <div class="faq-item">
            <button class="faq-question">
              Faut-il des compétences techniques en interne ?
              <i class="fas fa-chevron-down faq-icon"></i>
            </button>
            <div class="faq-answer">
              <div class="faq-answer-content">
                Non. Nous livrons des solutions prêtes à l'emploi et prenons en charge l'intégration et la maintenance.
              </div>
            </div>
          </div>

          <div class="faq-item">
            <button class="faq-question">
              Combien de temps pour voir un ROI ?
              <i class="fas fa-chevron-down faq-icon"></i>
            </button>
            <div class="faq-answer">
              <div class="faq-answer-content">
                Selon le projet : gains mesurables souvent en 1–3 mois pour optimisations prioritaires.
              </div>
            </div>
          </div>

          <div class="faq-item">
            <button class="faq-question">
              Quelle taille d'entreprise ciblez-vous ?
              <i class="fas fa-chevron-down faq-icon"></i>
            </button>
            <div class="faq-answer">
              <div class="faq-answer-content">
                PME & repreneurs — nous sommes spécialisés en post-reprise.
              </div>
            </div>
          </div>
        </div>
                        showSuccessModal();
                        form.reset();
                    } else {
                        console.error('Lead endpoint error', json);
                        errEl.textContent = 'Erreur : impossible d’envoyer votre demande pour le moment. Veuillez réessayer plus tard.';
                    }
                } catch (err) {
                    console.error(err);
                    errEl.textContent = "Erreur réseau : vérifiez votre connexion puis réessayez.";
                } finally {
                    if (submitBtn) submitBtn.disabled = false;
                }
            });
        }

        // Effets de parallaxe subtils
        function initParallax() {
          const hero = document.querySelector('.hero');
          if (!hero) return;
          window.addEventListener('scroll', () => {
            const scrolled = window.pageYOffset || document.documentElement.scrollTop;
              <div class="form-field">
                <label for="name" class="form-label">Nom complet</label>
                <input type="text" id="name" name="name" placeholder="Nom complet *" required>
              </div>
              <div class="form-field">
                <label for="company" class="form-label">Entreprise</label>
                <input type="text" id="company" name="company" placeholder="Entreprise *" required>
              </div>
              <div class="form-field">
                <label for="email" class="form-label">Email professionnel</label>
                <input type="email" id="email" name="email" placeholder="Email pro *" required>
              </div>
              <div class="form-field">
                <label for="phone" class="form-label">Téléphone</label>
                <input type="text" id="phone" name="phone" placeholder="Téléphone (optionnel)">
              </div>
              <div class="form-field">
                <label for="status" class="form-label">Statut</label>
                <select id="status" name="status">
        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            // Style initial pour les animations
            const animatedElements = document.querySelectorAll('.glass-card, .testimonial');
              </div>
              <div class="form-field">
                <label for="context" class="form-label">Contexte</label>
                <textarea id="context" name="context" placeholder="Résumé de votre situation (optionnel - pour qu'on sache comment vous aider au mieux)" rows="4"></textarea>
              </div>
                element.style.opacity = '0';
                element.style.transform = 'translateY(30px)';
                element.style.transition = 'all 0.6s ease';
            });
            
            // Initialisation des fonctionnalités
            initSmoothScroll();
            initParallax();
            
            // Animation initiale
            setTimeout(animateOnScroll, 500);
            
            // Animation au scroll
            let isScrolling = false;
            window.addEventListener('scroll', () => {
                if (!isScrolling) {
                    requestAnimationFrame(() => {
                        animateOnScroll();
                        isScrolling = false;
                    });
                    isScrolling = true;
                }
            });
        });

        // Micro-interactions avancées
        document.addEventListener('mousemove', function(e) {
            const cards = document.querySelectorAll('.glass-card');
            
            cards.forEach(card => {
                const rect = card.getBoundingClientRect();
                const x = e.clientX - rect.left;
                const y = e.clientY - rect.top;
                
                if (x >= 0 && x <= rect.width && y >= 0 && y <= rect.height) {
                    const centerX = rect.width / 2;
                    const centerY = rect.height / 2;
                    const rotateX = (y - centerY) / 10;
                    const rotateY = (centerX - x) / 10;
                    
                    card.style.transform = `perspective(1000px) rotateX(${rotateX}deg) rotateY(${rotateY}deg) translateZ(10px)`;
                } else {
                    card.style.transform = 'perspective(1000px) rotateX(0deg) rotateY(0deg) translateZ(0px)';
                }
            });
        });
    </script>

    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-XXXXXXX"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());

      gtag('config', 'G-XXXXXXX');
    </script>

    <script>
      const heroVariants = {
        A: {
          title: 'On repère. On automatise. <br><span class="neon-text">On fait gagner de l\'argent.</span>',
          subtitle: "Solutions (automatisation et IA) clés en main pour repreneurs et PME : diagnostic gratuit de 30 minutes et plan d'action concret."
        },
        B: {
          title: 'Rentabilisez votre reprise. <br><span class="neon-text">Sans perdre de temps.</span>',
          subtitle: 'Automatisation et IA appliquées à vos opérations : moins de coûts, plus de marge. Diagnostic offert, sans engagement.'
        },
        C: {
          title: 'Chaque minute compte. <br><span class="neon-text">Chaque euro aussi.</span>',
          subtitle: 'Nous analysons, optimisons et automatisons vos process. Résultat : efficacité immédiate et ROI tangible.'
        }
      };

      function setHero(variant) {
        const titleContainer = document.getElementById('hero-title-container');
        const subtitleContainer = document.getElementById('hero-subtitle-container');
        if (!titleContainer || !subtitleContainer) return;
        titleContainer.innerHTML = `<h1 class="text-hero hero-title">${heroVariants[variant].title}</h1>`;
        subtitleContainer.innerHTML = `<p class="text-lg hero-subtitle">${heroVariants[variant].subtitle}</p>`;

        const heroContent = document.querySelector('.hero-content');
        if (heroContent) {
          // ensure repaint and then show
          requestAnimationFrame(() => heroContent.classList.add('js-visible'));
        }
      }

      document.addEventListener('DOMContentLoaded', function() {
        const urlParams = new URLSearchParams(window.location.search);

                    // Fermer le menu mobile si ouvert
                    const mobileNav = document.getElementById('mobile-nav');
                    if (mobileNav && mobileNav.classList.contains('active')) {
                        mobileNav.classList.remove('active');
                    }
        const abParam = urlParams.get('ab'); // 'random' or 'B' or 'C'
        let variant = localStorage.getItem('hero_variant');

        if (!variant) {
        // Menu mobile
        function initMobileMenu() {
            const mobileToggle = document.querySelector('.mobile-menu-toggle');
            const mobileNav = document.getElementById('mobile-nav');
            const mobileClose = document.querySelector('.mobile-nav-close');

            if (mobileToggle && mobileNav) {
                mobileToggle.addEventListener('click', () => {
                    mobileNav.classList.add('active');
                });
            }

            if (mobileClose && mobileNav) {
                mobileClose.addEventListener('click', () => {
                    mobileNav.classList.remove('active');
                });
            }

            // Fermer en cliquant en dehors
            if (mobileNav) {
                mobileNav.addEventListener('click', (e) => {
                    if (e.target === mobileNav) {
                        mobileNav.classList.remove('active');
                    }
                });
            }
        }
          if (abParam === 'random') {
        // FAQ Accordéons
        function initFAQ() {
            const faqItems = document.querySelectorAll('.faq-item');
            
            faqItems.forEach(item => {
                const question = item.querySelector('.faq-question');
                const answer = item.querySelector('.faq-answer');
                
                if (question && answer) {
                
                // Supprimer l'ancien message d'erreur
                if (errEl) {
                    errEl.remove();
                }
                
                        const isActive = item.classList.contains('active');
                        
                        // Fermer tous les autres items
                        faqItems.forEach(otherItem => {
                            otherItem.classList.remove('active');
            initMobileMenu();
            initFAQ();
                            const otherAnswer = otherItem.querySelector('.faq-answer');
                            if (otherAnswer) {
                                otherAnswer.classList.remove('active');
                            }
                        });
                        
                        // Basculer l'item actuel
                        if (!isActive) {
                            item.classList.add('active');
                            answer.classList.add('active');
                        }
                    });
                }
            });
        }
            const keys = Object.keys(heroVariants);
            variant = keys[Math.floor(Math.random() * keys.length)];
          } else if (abParam === 'B' || abParam === 'C') {
            variant = abParam;
          } else {
            // default : A
                        showError('Erreur : impossible d'envoyer votre demande pour le moment. Veuillez réessayer plus tard.');
          }
          localStorage.setItem('hero_variant', variant);
        }
                    showError("Erreur réseau : vérifiez votre connexion puis réessayez.");
        if (typeof gtag === 'function') {
          gtag('event', 'hero_variant_shown', { variant: variant });
            let errEl = document.querySelector('.form-error');
          });

            function showError(message) {
                errEl = document.createElement('div');
                errEl.className = 'form-error';
                errEl.textContent = message;
                form.appendChild(errEl);
            }
        }
      });
    </script>

<!-- Success Modal (hidden by default) -->
<div id="success-modal" style="display:none; position:fixed; inset:0; background:rgba(0,0,0,0.7); z-index:10000; align-items:center; justify-content:center;">
  <div style="background:var(--glass-bg); padding:2rem; border-radius:12px; max-width:520px; text-align:center;">
    <h3 style="margin-bottom:0.5rem; color:var(--text-primary)">Demande reçue</h3>
    <p style="color:var(--text-muted); margin-bottom:1rem;">Merci, nous revenons vers vous sous 24h pour planifier le diagnostic.</p>
    <button class="btn btn-primary" onclick="document.getElementById('success-modal').style.display='none'">Parfait</button>
  </div>
</div>

<script>
  function showSuccessModal(){
    const modal = document.getElementById('success-modal');
    if(modal) modal.style.display = 'flex';
  }
</script>
</body>
</html>
